#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重复文件检测服务实现

基于新架构重构的重复文件检测服务，遵循RULE-001和RULE-003
使用DTO进行数据传输，通过事件总线进行通信
"""

import asyncio
import time
import uuid
from pathlib import Path
from typing import AsyncGenerator, Optional, List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

from src.services.interfaces import (
    IDuplicateDetectionService, IDuplicateActionService, IDuplicateAnalysisService,
    BaseServiceImpl, ServiceStatus, DuplicateResolutionStrategy
)
from src.data.dto.duplicate_dto import (
    DuplicateCheckRequest, DuplicateCheckResult, DuplicateGroup,
    DuplicateActionRequest, DuplicateCheckType, DuplicateAction
)
from src.data.dto.scan_dto import FileInfo
from src.data.dto.interface_dto import DetectionStatistics
from src.data.dto.base_dto import ProgressUpdate, ErrorInfo
from src.ui.events.event_bus import IEventBus
from src.ui.events.event_definitions import (
    create_business_event, BusinessEventType, EventPriority
)
from src.core.high_performance_duplicate_detector import HighPerformanceDuplicateDetector

# 常量定义
DEFAULT_MAX_WORKERS = 4
DEFAULT_BATCH_SIZE = 1000
DEFAULT_MIN_FILE_SIZE = 1024  # 1KB
DEFAULT_BLOOM_FILTER_SIZE = 100000
PROGRESS_UPDATE_INTERVAL = 0.5  # 进度更新间隔（秒）
HASH_CALCULATION_TIMEOUT = 300.0  # 哈希计算超时（秒）


class DuplicateDetectionServiceImpl(BaseServiceImpl, IDuplicateDetectionService):
    """
    重复文件检测服务实现
    
    遵循RULE-001: 模块职责单一原则 - 只负责重复文件检测
    遵循RULE-003: 事件驱动通信规范 - 通过事件总线通信
    遵循RULE-005: 异步任务实现 - 长时间运行的检测操作
    """
    
    def __init__(self, event_bus: IEventBus, max_workers: int = DEFAULT_MAX_WORKERS):
        super().__init__("DuplicateDetectionService", "2.0.0")
        self._event_bus = event_bus
        self._max_workers = max_workers
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 高性能检测器
        self._detector = HighPerformanceDuplicateDetector(
            enable_bloom_filter=True,
            max_workers=max_workers
        )
        
        # 任务管理
        self._detection_tasks: Dict[str, asyncio.Task] = {}
        self._detection_results: Dict[str, DuplicateCheckResult] = {}
        self._detection_progress: Dict[str, ProgressUpdate] = {}
        self._duplicate_groups: Dict[str, List[DuplicateGroup]] = {}
        
    async def start_service(self) -> bool:
        """启动服务"""
        if await super().start_service():
            # 发布服务启动事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_STARTED,
                self.get_service_name(),
                {"service_version": self.get_service_version(), "action": "service_started"}
            )
            self._event_bus.publish("SERVICE_STARTED", event)
            return True
        return False
    
    async def stop_service(self) -> bool:
        """停止服务"""
        # 取消所有活动的检测任务
        await self.cancel_all_tasks()
        
        # 关闭高性能检测器
        await self._detector.shutdown()
        
        # 关闭线程池
        self._executor.shutdown(wait=True)
        
        if await super().stop_service():
            # 发布服务停止事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,
                self.get_service_name(),
                {"action": "service_stopped"}
            )
            self._event_bus.publish("SERVICE_STOPPED", event)
            return True
        return False
    
    async def detect_duplicates(self, request: DuplicateCheckRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        异步检测重复文件
        
        遵循RULE-005: 长时间运行的操作实现为异步任务
        """
        # 验证请求
        validation_errors = await self.validate_detection_request(request)
        if validation_errors:
            error_info = ErrorInfo(
                code="DUPLICATE_VALIDATION_FAILED",
                message="重复检测请求验证失败",
                details="; ".join(validation_errors)
            )
            raise ValueError(error_info.message)
        
        # 发布检测开始事件
        event = create_business_event(
            BusinessEventType.DUPLICATE_CHECK_STARTED,
            self.get_service_name(),
            {
                "task_id": request.task_id,
                "directories": request.directories,
                "check_type": request.check_type.value
            }
        )
        self._event_bus.publish("DUPLICATE_CHECK_STARTED", event)
        
        # 创建检测任务
        detection_task = asyncio.create_task(self._execute_detection(request))
        self._detection_tasks[request.task_id] = detection_task
        
        try:
            # 异步生成进度更新
            async for progress in self._monitor_detection_progress(request.task_id):
                yield progress
                
                # 发布进度事件
                progress_event = create_business_event(
                    BusinessEventType.DUPLICATE_CHECK_PROGRESS,
                    self.get_service_name(),
                    progress.to_dict()
                )
                self._event_bus.publish("DUPLICATE_CHECK_PROGRESS", progress_event)
        
        except Exception as e:
            # 处理检测错误
            error_info = ErrorInfo(
                code="DUPLICATE_DETECTION_FAILED",
                message=f"重复检测执行失败: {str(e)}",
                details=f"任务ID: {request.task_id}"
            )
            
            error_event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,  # 使用现有事件类型
                self.get_service_name(),
                error_info.to_dict(),
                EventPriority.HIGH
            )
            self._event_bus.publish("DUPLICATE_CHECK_FAILED", error_event)
            raise
        
        finally:
            # 清理任务
            if request.task_id in self._detection_tasks:
                del self._detection_tasks[request.task_id]
    
    async def get_detection_result(self, task_id: str) -> Optional[DuplicateCheckResult]:
        """获取重复文件检测结果"""
        return self._detection_results.get(task_id)
    
    async def get_duplicate_groups(self, task_id: str) -> List[DuplicateGroup]:
        """获取重复文件组"""
        return self._duplicate_groups.get(task_id, [])
    
    async def get_duplicate_group(self, task_id: str, group_id: str) -> Optional[DuplicateGroup]:
        """获取指定的重复文件组"""
        groups = self._duplicate_groups.get(task_id, [])
        for group in groups:
            if group.group_id == group_id:
                return group
        return None
    
    async def filter_duplicate_groups(self, task_id: str, 
                                    min_file_count: int = 2,
                                    min_total_size: int = 0,
                                    file_extensions: Optional[List[str]] = None) -> List[DuplicateGroup]:
        """过滤重复文件组"""
        groups = self._duplicate_groups.get(task_id, [])
        filtered_groups = []
        
        for group in groups:
            # 检查文件数量
            if len(group.files) < min_file_count:
                continue
            
            # 检查总大小
            if group.total_size < min_total_size:
                continue
            
            # 检查文件扩展名
            if file_extensions:
                group_extensions = {Path(f.path).suffix.lower() for f in group.files}
                if not any(ext.lower() in group_extensions for ext in file_extensions):
                    continue
            
            filtered_groups.append(group)
        
        return filtered_groups
    
    async def calculate_space_savings(self, task_id: str, strategy: DuplicateResolutionStrategy) -> int:
        """计算空间节省量"""
        groups = self._duplicate_groups.get(task_id, [])
        total_savings = 0
        
        for group in groups:
            if len(group.files) < 2:
                continue
            
            # 根据策略计算保留的文件
            if strategy == DuplicateResolutionStrategy.KEEP_NEWEST:
                # 保留最新的文件，删除其他
                newest_file = max(group.files, key=lambda f: f.modified_time)
                savings = group.total_size - newest_file.size
            elif strategy == DuplicateResolutionStrategy.KEEP_OLDEST:
                # 保留最旧的文件，删除其他
                oldest_file = min(group.files, key=lambda f: f.modified_time)
                savings = group.total_size - oldest_file.size
            elif strategy == DuplicateResolutionStrategy.KEEP_LARGEST:
                # 保留最大的文件，删除其他
                largest_file = max(group.files, key=lambda f: f.size)
                savings = group.total_size - largest_file.size
            elif strategy == DuplicateResolutionStrategy.KEEP_SMALLEST:
                # 保留最小的文件，删除其他
                smallest_file = min(group.files, key=lambda f: f.size)
                savings = group.total_size - smallest_file.size
            else:
                # 默认保留第一个文件
                savings = group.total_size - group.files[0].size
            
            total_savings += max(0, savings)
        
        return total_savings
    
    async def validate_detection_request(self, request: DuplicateCheckRequest) -> List[str]:
        """验证重复检测请求"""
        errors = []
        
        # 使用DTO的内置验证
        dto_errors = request.validate()
        errors.extend(dto_errors)
        
        # 额外的业务验证
        for directory in request.directories:
            dir_path = Path(directory)
            if not dir_path.exists():
                errors.append(f"目录不存在: {directory}")
            elif not dir_path.is_dir():
                errors.append(f"路径不是目录: {directory}")
        
        return errors
    
    async def get_detection_statistics(self, task_id: str) -> Optional[DetectionStatistics]:
        """
        获取检测统计信息

        Args:
            task_id: 任务ID

        Returns:
            DetectionStatistics: 检测统计信息DTO，如果任务不存在返回None
        """
        result = self._detection_results.get(task_id)
        if not result:
            return None

        groups = self._duplicate_groups.get(task_id, [])

        # 计算平均组大小
        total_files_in_groups = sum(len(g.files) for g in groups)
        average_group_size = total_files_in_groups / max(1, len(groups)) if groups else 0

        # 计算最大组大小
        largest_group_size = max((len(g.files) for g in groups), default=0)

        return DetectionStatistics(
            task_id=task_id,
            total_files_checked=result.total_files_checked,
            total_duplicates_found=result.total_duplicates_found,
            total_space_wasted=result.total_space_wasted,
            duplicate_groups_count=len(groups),
            check_duration=result.check_duration,
            average_group_size=average_group_size,
            largest_group_size=largest_group_size
        )
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消检测任务"""
        if task_id in self._detection_tasks:
            task = self._detection_tasks[task_id]
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                # 任务被正常取消
                self._event_bus.publish("DUPLICATE_CHECK_CANCELLED", 
                    create_business_event(
                        BusinessEventType.DUPLICATE_CHECK_COMPLETED,
                        self.get_service_name(),
                        {"task_id": task_id, "reason": "user_cancelled"}
                    )
                )
            
            # 清理任务数据
            self._detection_tasks.pop(task_id, None)
            self._detection_progress.pop(task_id, None)
            
            # 发布取消事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,
                self.get_service_name(),
                {"task_id": task_id, "action": "cancelled"}
            )
            self._event_bus.publish("DUPLICATE_CHECK_CANCELLED", event)
            return True
        
        return False

    async def _execute_detection(self, request: DuplicateCheckRequest) -> DuplicateCheckResult:
        """执行重复文件检测的核心逻辑"""
        start_time = time.time()
        total_files_checked = 0
        total_duplicates_found = 0
        total_space_wasted = 0

        try:
            # 初始化进度
            progress = ProgressUpdate(
                task_id=request.task_id,
                progress=0.0,
                status_message="开始收集文件...",
                processed_items=0,
                total_items=0
            )
            self._detection_progress[request.task_id] = progress

            # 第一阶段：收集所有文件
            all_files = []
            for directory in request.directories:
                files = await self._collect_files_for_detection(directory, request)
                all_files.extend(files)

            total_files_checked = len(all_files)

            # 创建新的进度更新，包含总数信息
            progress = ProgressUpdate(
                task_id=request.task_id,
                progress=10.0,
                status_message=f"找到 {total_files_checked} 个文件，开始检测重复...",
                total_items=total_files_checked,
                processed_items=0
            )
            self._detection_progress[request.task_id] = progress

            # 第二阶段：使用高性能检测器检测重复文件
            duplicate_groups_dict = await self._detect_with_high_performance_detector(
                all_files, request
            )

            # 第三阶段：转换为DTO格式
            duplicate_groups = await self._convert_to_duplicate_groups(
                duplicate_groups_dict, request.check_type
            )

            # 计算统计信息
            total_duplicates_found = sum(len(group.files) for group in duplicate_groups)
            total_space_wasted = sum(group.duplicate_size for group in duplicate_groups)

            # 创建检测结果
            end_time = time.time()
            result = DuplicateCheckResult(
                task_id=request.task_id,
                duplicate_groups=duplicate_groups,
                total_files_checked=total_files_checked,
                total_duplicates_found=total_duplicates_found,
                total_space_wasted=total_space_wasted,
                check_duration=end_time - start_time
            )

            # 保存结果
            self._detection_results[request.task_id] = result
            self._duplicate_groups[request.task_id] = duplicate_groups

            # 发布完成事件
            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_COMPLETED,
                self.get_service_name(),
                result.to_dict()
            )
            self._event_bus.publish("DUPLICATE_CHECK_COMPLETED", event)

            return result

        except Exception as e:
            # 创建失败结果
            end_time = time.time()
            result = DuplicateCheckResult(
                task_id=request.task_id,
                duplicate_groups=[],
                total_files_checked=total_files_checked,
                total_duplicates_found=0,
                total_space_wasted=0,
                check_duration=end_time - start_time,
                errors=[str(e)]
            )

            self._detection_results[request.task_id] = result
            raise

    async def _collect_files_for_detection(self, directory: str, request: DuplicateCheckRequest) -> List[str]:
        """收集用于检测的文件"""
        files = []
        dir_path = Path(directory)

        try:
            if request.recursive:
                pattern = "**/*" if request.include_hidden else "**/[!.]*"
                for file_path in dir_path.glob(pattern):
                    if self._should_include_file(file_path, request):
                        files.append(str(file_path))
            else:
                pattern = "*" if request.include_hidden else "[!.]*"
                for file_path in dir_path.glob(pattern):
                    if file_path.is_file() and self._should_include_file(file_path, request):
                        files.append(str(file_path))

        except Exception as e:
            # 记录错误但继续处理
            error_info = ErrorInfo(
                code="DIRECTORY_SCAN_FAILED",
                message=f"目录扫描失败: {directory}",
                details=str(e)
            )

            event = create_business_event(
                BusinessEventType.DUPLICATE_CHECK_PROGRESS,
                self.get_service_name(),
                error_info.to_dict()
            )
            self._event_bus.publish("DUPLICATE_CHECK_ERROR", event)

        return files

    def _should_include_file(self, file_path: Path, request: DuplicateCheckRequest) -> bool:
        """判断是否应该包含文件"""
        if not file_path.is_file():
            return False

        try:
            file_size = file_path.stat().st_size

            # 检查文件大小
            if file_size < request.min_file_size:
                return False

            if request.max_file_size and file_size > request.max_file_size:
                return False

            # 检查文件扩展名
            if request.file_extensions:
                file_ext = file_path.suffix.lower()
                if file_ext not in [ext.lower() for ext in request.file_extensions]:
                    return False

            # 检查排除模式
            if request.exclude_patterns:
                file_name = file_path.name
                for pattern in request.exclude_patterns:
                    if pattern in file_name:
                        return False

            return True

        except Exception:
            return False

    async def _detect_with_high_performance_detector(self, files: List[str],
                                                   request: DuplicateCheckRequest) -> Dict[str, Any]:
        """使用高性能检测器检测重复文件"""

        def progress_callback(progress: float, message: str = ""):
            """进度回调函数"""
            try:
                current_progress = self._detection_progress.get(request.task_id)
                if current_progress:
                    # 将检测器进度映射到总进度的10%-90%
                    total_progress = 10.0 + (progress * 80.0)
                    # 创建新的进度更新实例
                    updated_progress = ProgressUpdate(
                        task_id=current_progress.task_id,
                        progress=min(total_progress, 90.0),
                        status_message=message or f"检测进度: {progress:.1f}%",
                        total_items=current_progress.total_items,
                        processed_items=current_progress.processed_items
                    )
                    self._detection_progress[request.task_id] = updated_progress
            except Exception:
                pass  # 忽略进度更新错误

        # 使用高性能检测器
        return await self._detector.detect_duplicates(
            files,
            progress_callback=progress_callback,
            interrupt_event=None
        )

    async def _convert_to_duplicate_groups(self, detector_groups: Dict[str, Any],
                                         check_type: DuplicateCheckType) -> List[DuplicateGroup]:
        """将检测器结果转换为DTO格式"""
        duplicate_groups = []

        for group_id, group_data in detector_groups.items():
            if hasattr(group_data, 'files') and len(group_data.files) >= 2:
                # 转换文件信息
                file_infos = []
                total_size = 0

                for file_path in group_data.files:
                    try:
                        path = Path(file_path)
                        stat = path.stat()

                        file_info = FileInfo(
                            path=str(path),
                            name=path.name,
                            size=stat.st_size,
                            modified_time=stat.st_mtime,
                            extension=path.suffix.lower(),
                            is_directory=False
                        )

                        file_infos.append(file_info)
                        total_size += stat.st_size

                    except Exception:
                        continue  # 跳过无法访问的文件

                if len(file_infos) >= 2:
                    # 计算重复占用的空间（保留一个文件）
                    duplicate_size = total_size - file_infos[0].size

                    duplicate_group = DuplicateGroup(
                        group_id=group_id,
                        files=file_infos,
                        total_size=total_size,
                        duplicate_size=duplicate_size,
                        check_type=check_type,
                        confidence=getattr(group_data, 'confidence', 1.0)
                    )

                    duplicate_groups.append(duplicate_group)

        return duplicate_groups

    async def _monitor_detection_progress(self, task_id: str) -> AsyncGenerator[ProgressUpdate, None]:
        """监控检测进度"""
        last_progress = 0.0

        while task_id in self._detection_tasks:
            current_progress = self._detection_progress.get(task_id)

            if current_progress and current_progress.progress > last_progress:
                yield current_progress
                last_progress = current_progress.progress

                # 如果完成，退出监控
                if current_progress.progress >= 100.0:
                    break

            # 等待一段时间再检查
            await asyncio.sleep(PROGRESS_UPDATE_INTERVAL)

        # 确保最终进度被发送
        final_progress = self._detection_progress.get(task_id)
        if final_progress:
            final_progress.progress = 100.0
            final_progress.status_message = "检测完成"
            yield final_progress

    # 实现IValidationService的抽象方法
    def validate_request(self, request) -> List[str]:
        """验证请求数据"""
        if hasattr(request, 'validate'):
            return request.validate()
        return []

    def validate_file_path(self, file_path: str) -> List[str]:
        """验证文件路径"""
        errors = []
        path = Path(file_path)

        if not path.exists():
            errors.append(f"文件不存在: {file_path}")
        elif not path.is_file():
            errors.append(f"路径不是文件: {file_path}")

        return errors

    def validate_directory_path(self, directory_path: str) -> List[str]:
        """验证目录路径"""
        errors = []
        path = Path(directory_path)

        if not path.exists():
            errors.append(f"目录不存在: {directory_path}")
        elif not path.is_dir():
            errors.append(f"路径不是目录: {directory_path}")

        return errors

    def validate_file_operation(self, operation: str, file_paths: List[str]) -> List[str]:
        """验证文件操作"""
        errors = []

        if operation not in ["detect", "analyze", "compare"]:
            errors.append(f"不支持的操作: {operation}")

        for file_path in file_paths:
            file_errors = self.validate_file_path(file_path)
            errors.extend(file_errors)

        return errors

    def create_task_id(self) -> str:
        """创建新的任务ID"""
        import uuid
        return str(uuid.uuid4())

    def get_active_tasks(self) -> List[str]:
        """获取活动任务列表"""
        return list(self._detection_tasks.keys())
